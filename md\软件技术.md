|2 - 软件技|技术|
|---|---|
|Status|Waiting|
|Project|数字产品<br>护照|
|Tags||
|Task ID|TAS-278|
|repetitive tasks||
|Archived||



3. 数据层 ‒ Amazon Aurora (PostgreSQL / MySQL)

## 沟通


快速构建 MVP ，如果非常了解另一种方案的同样实现，可以替换。如果只是有猜测，则先用

当前技术，之后再优化。


前端 app : iOS, Android, 小程序


前段 web : Nuxtjs, tailwind CSS


后端： Go


用户系统： Keycloak


2 - 软件技术 1


数据库： postgreSQL ( Amazon Aurora )


环境： AWS ( ECS Fargate, Fargate Task, Aurora Serverless v2,MSK Serverless,S3


+ CloudFront, Terraform + GitHub Actions)

## 技术架构


下面按 **DPPaaS** （ **Digital Product Passport as a Service** ） 的典型分层，把


Nuxt .js + Go + Aurora + Kafka 各自承担的职责与协作链路拆解出来，方便你在架构图或 PRD 里直







|1. 表现层 ‒ N|Nuxt .js（Vue 3）|
|---|---|
|职责|典型实现要点|
|多租户门户** UI**|- 登陆/注册、品牌后台、质检机构后台。- SFC + Pinia/Composable管理状态；<br>按租户注入主题、权限。|
|数字护照视图** &**编<br>辑器|- SSR/SSG提供 SEO，可生成可分享 URL ( /passport/{id} ).-可视化表单（分步<br>Wizard）上传物料、碳足迹、认证证书(PDF)。|
|实时通知|- 订阅后端推送的 passport_updated  WebSocket频道，Toast/Badge提示。|
|**i18n + A11y**|- 内置多语言包（EN/DE/FR/ZH），对接欧盟无障碍检查。|
|边缘渲染|- Nuxt 3 Nitro输出 Cloudflare Workers，首屏 TTFB≈2-5 ms；无需额外 Node<br>进程。|

### 与 Next.js 的差别：仅限于 UI 技术栈（ React → Vue ）， API 合同、
### Cookie/OIDC 流程保持不变； BFF 如仍想写在前端仓，可用

### Nuxt server/ 目录或继续让 Go 统一暴露。

## 2. API / 业务层 ‒ Go 微服务

|服务|关键功能|对接外部|
|---|---|---|
|**passport-service**|CRUD、版本比对、生成 QR / NFC payload；写 Aurora；<br>发 passport_created / updated 事件到 Kafka。|REST/gRPC|
|**registry-service**|统一 ProductID⇄ PassportID解析、公开查询 API；对外限<br>流&审计。|REST|



2 - 软件技术 2


|compliance-<br>service|校验供应链证书、碳足迹算法（ISO 14067）；异步校验任务<br>入 Kafka。|gRPC / Async<br>job|
|---|---|---|
|**auth-service**|OIDC provider /租户 RBAC；签 JWT，供 Nuxt &其他服务<br>验证。|OIDC / gRPC|
|**notification-**<br>**service**|监听 Kafka事件 →推邮件、WebSocket、SMS。|Kafka / REST|

### - 写库 发事件 的 DDD 反应堆模式：每个 Go 服务先事务性写 Aurora ，

### 再把领域事件写入 Kafka → 下游消费。
















|3. 数据层 ‒|Amazon Aurora (PostgreSQL / MySQ|QL)|
|---|---|---|
|模块|存什么|亮点|
|**Core DB**|passports, product_items, suppliers, audits, users, tenants …|ACID、跨 AZ高<br>可用，支持<br>JSONB存半结<br>构化属性。|
|**History DB /**分<br>区表|护照历史、变更快照，按月 RANGE + PARTITION，便于 GDPR<br>删除。||
|**Read Replicas**|供报告/BI、全文索引任务，隔离 OLAP读压。||
|**Logical**<br>**Replication**|可把变更流灌入 Kafka（Debezium）→ Elasticsearch近实时搜<br>索。||






















|4. 事件 & 集成层 ‒ Apa|ache Kafka|Col3|Col4|
|---|---|---|---|
|Topic范围|发布者|订阅者|用途|
|passport_created, passport_updated|passport-service|notification-<br>service, search-<br>indexer|更新 UI、推送、<br>重建索引|
|compliance_check_requested|Nuxt调用 Go API →<br>compliance-<br>service|compliance-<br>service(worker)|触发异步校验|
|compliance_check_result|compliance-<br>service|passport-service,<br>notification-<br>service|写回结果、提醒审<br>核人|



2 - 软件技术 3


|cdc_coredb|Debezium (Aurora)|analytics-pipeline|数据湖增量拉链、<br>BI 报表|
|---|---|---|---|
|external_webhook_out|各 Go服务|partner-gateway|向外部 ERP /<br>PLM广播|

### Kafka 在 解耦（写库即发事件）与 可观测（链路追踪、重放）上是

### DPPaaS 核心胶水。你也可用 Confluent Cloud Serverless ，省管集

Nuxt ➜ POST /passports ➜ passport-service （ Go ）


↳ 写 Aurora （ tx ） ↳ passport_created 事件 ➜ Kafka


↳ notification-service 推 WebSocket ➜ Nuxt UI 刷新


Nuxt 提交 ➜ compliance_check_requested ➜ Kafka


compliance-service worker 拉任务 ➜ 调 ISO14067 引擎 ➜ 输出结果


↳ compliance_check_result ➜ passport-service 更新状态 + Kafka


CDC Stream ( cdc_coredb ) ➜ partner-gateway 过滤规则 ➜ ERP/PLM Webhook

|总结|Col2|Col3|
|---|---|---|
|层|主要技术|在 DPPaaS中的角色|
|前端|**Nuxt .js**|多租户门户、护照展示/编辑、SEO SSR、实时通知|
|业务|**Go**微服务|核心领域逻辑、API、鉴权、事件发布|
|数据|**Aurora**|关系数据、版本快照、读写分离、CDC|
|事件|**Kafka**|域事件总线、异步任务、外部集成、可重放|



把 Next 换成 Nuxt 后，其余 Aurora + Kafka + Go 设计完全沿用；前端与后端之间仍走
REST/gRPC ， Kafka 事件契约不变，因此 风险集中在 **UI** 层迁移，后端与数据流可以 “ 零感
知 ” 过渡。


2 - 软件技术 4


如果你需要更细的部署脚本（ Terraform / Helm chart ）或事件 schema （ Avro / Protobuf ）


完全掌控、 **EU** 数据驻留、插件细节多 → **Keycloak**


多租户、 _Passkeys_ 已补足，唯一代价是自己打补丁与监控。


快速起盘、 **SDK** 丰富、 **B2C + B2B** 全覆盖 → **Auth0**


但要注意： _Organizations_ 、 _MFA_ 、企业 _SSO_ 都是阶梯计费，规模化后 _TCO_ 飙升。


预算敏感、前端团队主导、功能 **≈ Auth0 80 %** 就够 → **Clerk**


首 _10 k MAU_ 免费 _+ $0.02/MAU_ 线性计费，代码集成对前端更友好。


2 - 软件技术 5


