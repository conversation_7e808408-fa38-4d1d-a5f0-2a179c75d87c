#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转Markdown转换脚本
保持原有格式和内容不变
"""

import os
import sys
from pathlib import Path
import pymupdf4llm

def create_output_directory(output_dir):
    """创建输出目录"""
    try:
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        print(f"✓ 输出目录已创建: {output_dir}")
        return True
    except Exception as e:
        print(f"✗ 创建输出目录失败: {e}")
        return False

def convert_pdf_to_markdown(pdf_path, output_path):
    """将PDF转换为Markdown"""
    try:
        print(f"开始转换: {pdf_path}")
        
        # 检查PDF文件是否存在
        if not os.path.exists(pdf_path):
            print(f"✗ PDF文件不存在: {pdf_path}")
            return False
        
        # 使用pymupdf4llm转换PDF到Markdown
        md_text = pymupdf4llm.to_markdown(pdf_path)
        
        # 保存Markdown文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(md_text)
        
        print(f"✓ 转换完成: {output_path}")
        return True
        
    except Exception as e:
        print(f"✗ 转换失败 {pdf_path}: {e}")
        return False

def main():
    """主函数 - 批量处理PDF文件"""
    print("=== PDF转Markdown转换工具 ===")
    
    # 定义输入和输出路径
    input_files = [
        "0_-_概括.pdf",
        "软件技术.pdf"
    ]
    output_dir = "md"
    
    # 创建输出目录
    if not create_output_directory(output_dir):
        sys.exit(1)
    
    # 转换统计
    success_count = 0
    total_count = len(input_files)
    
    # 批量转换PDF文件
    for pdf_file in input_files:
        # 生成输出文件名
        output_file = os.path.join(output_dir, os.path.splitext(pdf_file)[0] + ".md")
        
        # 执行转换
        if convert_pdf_to_markdown(pdf_file, output_file):
            success_count += 1
        
        print("-" * 50)
    
    # 显示转换结果
    print(f"\n=== 转换完成 ===")
    print(f"成功转换: {success_count}/{total_count} 个文件")
    
    if success_count == total_count:
        print("✓ 所有PDF文件转换成功!")
    else:
        print(f"✗ {total_count - success_count} 个文件转换失败")

if __name__ == "__main__":
    main()
