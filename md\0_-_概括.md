|0 - 概括|Col2|
|---|---|
|Status|Waiting|
|Project|数字产品<br>护照|
|Tags||
|Task ID|TAS-274|
|repetitive tasks||
|Archived||



0 - 概括 1


|名称|英文|说明|
|---|---|---|
|EcoDesign<br>生态设计|**Ecological design**|指引生产方，运输方，认证<br>方，回收方在产品的各个环节<br>进行环保节能设计的一套理论<br>和要求。|
|DPP<br>数字产品护照|Digital Product Passport|数字产品护照包含产品的信<br>息，DPP实际上是一段保存了<br>产品信息，通过加密技术能确<br>保信息不被修改的数据。|
|DPP Registry<br>数字产品护照注册库|DPP Registry|数字产品护照注册库是欧委会<br>运营的数字产品护照“中央索<br>引”，用于快速找到数字产品护<br>照的真实位置。|
|EORI<br>经营者注册识别号|economic operator<br>registration and<br>identification|经营者注册识别号是欧盟公司<br>唯一海关识别号码|
|DID|Decentralized identifier|由政府机构或者信任机构给某<br>个实体办法的数字身份。|
|VC<br>可识别凭证|Verifiable Credential|可识别凭证，由DID进行加密数<br>字签字后，可展示的一种凭<br>证。包含：签发人，有效日<br>期，授予人，凭证内容。|

### DPP - 数字产品护照





数字产品护照是一份电子数据，记录产品从生产，加工，认证，运输，销售，到回收过程

中的所有记录，确保数据完整和可查。

#### DPP Registry 数字产品护照索引中央库


数字产品护照索引就是一系列的数据，提交到 DPP Registry, 以便各个单位查验和查询商


0 - 概括 2


UID ：物品唯一代码，可以是编码，也可以是 URL ，甚至其他形式。


使用 gs1 数字连接的好处是会自动跳转到产品信息 url ，任何设备扫码后都可以直


型号级： https://id.gs1.org/01/<GTIN> 可以获得对应 PDD 的信息


单件级： …/01/<GTIN>/21/<Serial> 可以获得单件商品信息


如果使用编码，则需要特定的程序，或者需要扫码者先登录到特定的网站


dataCarrierDigest ：加密指纹，用于验证数字产品护照索引的信息正确。


确保 uid, dppEndpoint 都没被修改，找到的是正确的产品以及产品信息索取地


即使托管者破产后，产品数据依旧要保持可查（ 修改地址到其他地方）


serviceProviderId ：数字产品护照的托管者 ID


economicOperatorEORI ： 经营者的 EORI 号码


数字产品护照是储存在 DPPaaS 平台上的产品信息。


0 - 概括 3


“ ” “ ” “ ”
官网目前要求的是 机器可读 和 数字签名 和 不可篡改 。


对于机器可读，目前最流行的是 EPCIS 2.0 事件流标准。


OpenEPCIS 也支持单条记录签名（但不是最适合 DPP 的）。


DPP 里包含了基础要求的 40 个核心字段 + 行业附录 ( 可选），本质上 DPP 是一份 Json-LD 数





任何查询机构可以在能够显示 JSON-LD 的机器上，通过 Profile 和 context 的规范来显示


JSON-LD 的数据。（因为 JSON- LD 里贵方了数据类型和用法）


数字产品护照保证完整性的方式：数字签名 或 **VC**


数字签名｜在持有者通过自己的密钥加密文件，保证文件无法被串改。


{

"uid": "iso15559:ACME:**********",


0 - 概括 4


"material": "cotton",

"recycleRate": 0.85,

"sig": "MEUCIQCng...<base64-of-signature>",

"sigAlg": "ECDSA_P256_SHA256",

"pubKey": "did:key:z6Mkegn..."

}


VC ｜ VC 用到了数字签名的技术，但是在文件中加入了更多的信息：签名人信息，文件状

态，文件有效期，文件状态验证地址等。可以在文件被吊销的情况下快速更改文件的有效


VC 大部分情况是由持有 DID 的人或者机构签署。


{

"@context": ["https://www.w3.org/ns/credentials/v2", "..."],


"id": "urn:uuid:7d9f...",

"type": ["VerifiableCredential","ProductPassport"],

"issuer": "did:example:acme",

"credentialSubject": {

"id": "iso15559:ACME:**********",

"material": "cotton",


"recycleRate": 0.85

},

"credentialStatus": {

"type": "BitstringStatusList2021",

"statusListIndex": "12345",

"statusListCredential": "https://psp.example.com/sl/2024-10"

},


"proof": {

"type": "Ed25519Signature2020",

"created": "2025-01-15T10:12:54Z",

"verificationMethod": "did:example:acme#key1",

"proofPurpose": "assertionMethod",

"proofValue": "zXCtnw..."


0 - 概括 5


}

}


文件机构

### 场景例子


生产环节


0 - 概括 6


|角色|描述|
|---|---|
|经营者|经营者必须拥有欧洲实体，如果从中国经货，而中国工厂没有欧洲实体，可<br>以通过有资质的公司进行认证（待确定具体认证方式）后得到VC。或者以自<br>己的名义担保中国工厂的资料信息，自己签署VC。<br>经营者在销售产品之前，需要向DPPaaS平台申请一个给产品的DPP（含产品<br>唯一识别码UID),经营者将DPP生成的二维码（或NFC）贴在产品上。|
|第三方机构|第三方检测机构给产品出VC后，将VC交给经营者，经营者再上传到DPPaaS<br>进行管理。（第三方检测机构也可以在有授权的情况下，直接上传到DPPaaS<br>平台）|
|物流商|物流商将DPP提交给海关进行申报|


清关环节


0 - 概括 7


|角色|描述|
|---|---|
|海关|海关通过扫码得到产品的UID号后，在DPP Registry里查到对应的号码是否有<br>效，如果有效，再从DPP Registry的索引里找到DPPaaS提供的网络地址，<br>然后获取完整的DPP。<br>获得完整DPP后，海关会对里面的VCs进行验证完整性以及当前有效性。|
|DPPaaS平台|负责提供数字产品护照数据及产品的可认证凭证（VCs)|
|第三方检测机构|维护自己签字的VC的有效性，如果无效，需要及时更新|

### DPPaaS - 数字产品护照平台

DPPaaS 在拿到 DPP 资质后，可以向欧委会里注册 “ 写入 ” 数字产品护照。


0 - 概括 8


DPPaaS 只做为数字产品护照以及产品对应的信息的 “ 托管者 ” （含注册颁发）。

#### 核心价值


拥有资质的数字产品护照托管平台（ DPP as a Service )


数字身份颁发，数字签名功能实现


0 - 概括 9


#### 关键资源

DPP 资质


经营者


检测机构 (VCs 部分 )


消费者

#### 关键合作伙伴


律师


检测机构 ( 检测部分 )


跨境电商服务商

### 时间线


EcoDesign 是 2025-2030 年欧盟在市场产品规范化上的重点。

|时间节点|内容|
|---|---|
|2024-07-18|ESPR生效、DPP写进欧盟法|
|2025-04-16|ESPR-首份工作计划锁定首批 11组（纺织、家具、电子…）|
|2026-07-19|DPP注册库必须正式上线（Article 13）|
|2027-02-18|电池类产品强制使用数字产品护照|
|2027-07|纺织&家具正式强制使用产品护照（18个月过渡完）|
|2028-2030|注册库+授权条例持续扩面，其余品类在欧盟条例下根据品类进行扩展|



0 - 概括 10


### 第一批受影响的行业

其他产品类目可在条款大框架下，根据品类需求进行扩展。


















|名称|说明|重点产品|
|---|---|---|
|铁和钢 (iron & steel)|高耗能、高排放的基础材料，碳环保，可循<br>环利用|餐具，厨具，园艺工具，<br>器械，五金|
|铝 (aluminium)|高耗能、高排放的基础材料，碳环保，可循<br>环利用|灯饰，配件，外壳，园艺<br>工具，包装，通信设备|
|纺织品（尤其服装与鞋<br>类） (textiles, in<br>particular garments<br>& footwear)|服装、鞋履、家用纺织品|所有|
|家具（含床垫）<br>(furniture, including<br>mattresses)|家具及其填充物|所有|
|轮胎 (tyres)|乘用车、商用车轮胎|轮胎销量其实也不少|
|洗涤剂 (detergents)|家用和工业清洁剂||
|涂料/油漆 (paints)|涂料、清漆等||
|润滑剂 (lubricants)|工业与汽车用油||
|化学品 (chemicals)|重点关注大宗有机/无机化工品||
|能源相关产品<br>(energy-related<br>products to be<br>regulated for the first<br>time / to be<br>reviewed)|以前未被旧能效指令覆盖、或需更新的能源<br>类产品|充电宝，汽车电池，可穿<br>戴设备|
|信息与通信技术产品及<br>其他电子产品 (ICT<br>products and other<br>electronics)|含计算机、手机、小家电等||




纺织品类：同批生产的共用一个数字产品护照，对于高端产品，可单独使用一个子编号。


0 - 概括 11


### 盈利模式

核心： 通过销售 “ 护照（二维码） ” 及护照的管理，对每一个打二维码的产品收费（欧盟强

制要求进入欧洲的产品都要有数字产品护照）


与回收机构合作，让消费者把不需要的产品，通过扫码后进行回收。回收机构把回购价汇

给平台，平台转换成抵扣券发给消费者，消费者可以在平台购买环保产品。


0 - 概括 12


