# DPPaaS 数字产品护照平台 - 系统架构设计

## 整体架构概览

```
┌─────────────────────────────────────────────────────────────────┐
│                        用户接入层                                │
├─────────────────────────────────────────────────────────────────┤
│  Web Portal    │  Mobile Apps   │  小程序  │  API Clients      │
│  (Nuxt.js)     │  (iOS/Android) │         │  (第三方集成)      │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        API网关层                                │
├─────────────────────────────────────────────────────────────────┤
│  负载均衡  │  API Gateway  │  认证授权  │  限流熔断  │  监控日志  │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        微服务层                                 │
├─────────────────────────────────────────────────────────────────┤
│ passport-service │ registry-service │ compliance-service │ ... │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                        数据存储层                               │
├─────────────────────────────────────────────────────────────────┤
│  Aurora PostgreSQL  │  Redis  │  S3  │  Elasticsearch  │ Kafka │
└─────────────────────────────────────────────────────────────────┘
```

## 详细架构设计

### 1. 前端架构层

#### 1.1 Web门户 (Nuxt.js)
```
Web Portal Architecture
├── pages/                    # 页面路由
│   ├── auth/                # 认证相关页面
│   ├── dashboard/           # 仪表板
│   ├── passport/            # 数字护照管理
│   └── admin/               # 管理后台
├── components/              # 可复用组件
│   ├── common/              # 通用组件
│   ├── passport/            # 护照相关组件
│   └── forms/               # 表单组件
├── composables/             # 组合式函数
├── middleware/              # 中间件
├── plugins/                 # 插件
└── stores/                  # Pinia状态管理
```

**关键特性：**
- **多租户支持：** 基于域名/路径的租户识别
- **国际化：** 支持中文、英文、德文、法文
- **响应式设计：** 适配桌面端和移动端
- **SEO优化：** SSR/SSG支持搜索引擎优化
- **实时通知：** WebSocket连接实时更新

#### 1.2 移动端应用
```
Mobile Apps
├── iOS App (Swift/SwiftUI)
│   ├── 扫码功能
│   ├── 护照查看
│   └── 离线缓存
├── Android App (Kotlin)
│   ├── 扫码功能
│   ├── 护照查看
│   └── 离线缓存
└── 微信小程序
    ├── 轻量级查看
    ├── 分享功能
    └── 微信生态集成
```

### 2. API网关层

#### 2.1 网关架构
```
API Gateway
├── 路由管理
│   ├── 服务发现
│   ├── 负载均衡
│   └── 健康检查
├── 安全控制
│   ├── 认证验证
│   ├── 权限控制
│   └── API密钥管理
├── 流量控制
│   ├── 限流策略
│   ├── 熔断机制
│   └── 重试逻辑
└── 监控日志
    ├── 请求追踪
    ├── 性能监控
    └── 错误日志
```

### 3. 微服务架构层

#### 3.1 核心业务服务

##### passport-service (护照服务)
```
passport-service/
├── api/                     # API接口层
│   ├── handlers/           # HTTP处理器
│   ├── middleware/         # 中间件
│   └── routes/             # 路由定义
├── domain/                 # 领域层
│   ├── entities/           # 实体
│   ├── repositories/       # 仓储接口
│   └── services/           # 领域服务
├── infrastructure/         # 基础设施层
│   ├── database/           # 数据库实现
│   ├── cache/              # 缓存实现
│   └── messaging/          # 消息队列
└── application/            # 应用层
    ├── commands/           # 命令处理
    ├── queries/            # 查询处理
    └── events/             # 事件处理
```

**核心功能：**
- 数字护照CRUD操作
- 版本管理和历史追踪
- QR码/NFC载荷生成
- 数字签名验证
- 事件发布到Kafka

##### registry-service (注册服务)
```
registry-service/
├── 产品ID ⇄ 护照ID映射
├── 公开查询API
├── DPP Registry对接
├── 限流和审计
└── 缓存优化
```

##### compliance-service (合规服务)
```
compliance-service/
├── 供应链证书校验
├── 碳足迹计算 (ISO 14067)
├── VC (可验证凭证) 验证
├── 异步校验任务
└── 合规报告生成
```

##### auth-service (认证服务)
```
auth-service/
├── OIDC Provider
├── 多租户RBAC
├── JWT签发和验证
├── DID管理
└── 密钥管理
```

#### 3.2 服务间通信

```
服务通信模式
├── 同步通信
│   ├── REST API (外部接口)
│   ├── gRPC (内部服务)
│   └── GraphQL (复杂查询)
└── 异步通信
    ├── Kafka事件
    ├── 消息队列
    └── WebSocket推送
```

### 4. 数据存储架构

#### 4.1 数据库设计

##### 主数据库 (Aurora PostgreSQL)
```sql
-- 核心表结构
CREATE TABLE tenants (
    id UUID PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    settings JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE passports (
    id UUID PRIMARY KEY,
    tenant_id UUID REFERENCES tenants(id),
    product_id VARCHAR(255) NOT NULL,
    uid VARCHAR(255) UNIQUE NOT NULL,
    data JSONB NOT NULL,
    signature TEXT,
    version INTEGER DEFAULT 1,
    status VARCHAR(50) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE passport_history (
    id UUID PRIMARY KEY,
    passport_id UUID REFERENCES passports(id),
    version INTEGER NOT NULL,
    data JSONB NOT NULL,
    operation VARCHAR(50) NOT NULL,
    operator_id UUID,
    created_at TIMESTAMP DEFAULT NOW()
) PARTITION BY RANGE (created_at);

-- 分区表 (按月分区)
CREATE TABLE passport_history_2024_01 PARTITION OF passport_history
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

##### 缓存设计 (Redis)
```
Redis缓存策略
├── 会话缓存
│   ├── user:session:{token}
│   └── tenant:config:{id}
├── 数据缓存
│   ├── passport:{id}
│   ├── product:{uid}
│   └── registry:{product_id}
├── 分布式锁
│   └── lock:passport:{id}
└── 消息队列
    └── queue:notifications
```

#### 4.2 事件流架构

##### Kafka主题设计
```
Kafka Topics
├── passport-events
│   ├── passport.created
│   ├── passport.updated
│   ├── passport.deleted
│   └── passport.verified
├── compliance-events
│   ├── compliance.check.requested
│   ├── compliance.check.completed
│   └── compliance.check.failed
├── audit-events
│   ├── user.login
│   ├── api.access
│   └── data.export
└── integration-events
    ├── external.webhook.out
    └── cdc.database.changes
```

### 5. 安全架构

#### 5.1 认证授权流程
```
认证流程
1. 用户登录 → Keycloak
2. 获取JWT Token
3. Token验证 → auth-service
4. 权限检查 → RBAC
5. 访问资源
```

#### 5.2 数据安全
```
数据安全措施
├── 传输安全
│   ├── TLS 1.3加密
│   ├── 证书管理
│   └── HSTS策略
├── 存储安全
│   ├── 数据库加密
│   ├── 文件加密
│   └── 密钥轮换
├── 访问控制
│   ├── 多因素认证
│   ├── IP白名单
│   └── 审计日志
└── 合规性
    ├── GDPR合规
    ├── 数据删除
    └── 隐私保护
```

### 6. 部署架构

#### 6.1 云基础设施
```
AWS部署架构
├── 网络层
│   ├── VPC (Virtual Private Cloud)
│   ├── 子网 (公有/私有)
│   ├── 安全组
│   └── NAT网关
├── 计算层
│   ├── ECS Fargate (容器运行)
│   ├── Application Load Balancer
│   ├── Auto Scaling Group
│   └── CloudFront CDN
├── 存储层
│   ├── Aurora Serverless v2
│   ├── ElastiCache Redis
│   ├── S3存储桶
│   └── EFS文件系统
└── 监控层
    ├── CloudWatch监控
    ├── X-Ray链路追踪
    ├── CloudTrail审计
    └── Config合规检查
```

#### 6.2 多环境部署
```
环境架构
├── 开发环境 (dev)
│   ├── 单实例部署
│   ├── 开发数据库
│   └── 基础监控
├── 测试环境 (test)
│   ├── 集成测试
│   ├── 性能测试
│   └── 安全测试
├── 预生产环境 (staging)
│   ├── 生产级配置
│   ├── 压力测试
│   └── 灾备演练
└── 生产环境 (prod)
    ├── 多可用区部署
    ├── 自动扩缩容
    ├── 完整监控
    └── 灾备机制
```

### 7. 监控运维架构

#### 7.1 监控体系
```
监控架构
├── 应用监控
│   ├── APM (Application Performance Monitoring)
│   ├── 业务指标监控
│   ├── 错误率监控
│   └── 响应时间监控
├── 基础设施监控
│   ├── 服务器资源监控
│   ├── 网络监控
│   ├── 数据库监控
│   └── 存储监控
├── 日志管理
│   ├── 集中式日志收集
│   ├── 日志分析
│   ├── 日志告警
│   └── 日志归档
└── 告警通知
    ├── 邮件告警
    ├── 短信告警
    ├── 钉钉/企微告警
    └── 电话告警
```

这个架构设计充分考虑了DPPaaS平台的业务需求，在可扩展性、安全性、合规性方面都有完善的设计。
